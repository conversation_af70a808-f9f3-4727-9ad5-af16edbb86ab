import React from 'react';
import { useApp } from '../context/AppContext';
import Toast from './Toast';
import ModalLogin from './Modals/ModalLogin';
import ModalRegister from './Modals/ModalRegister';

// مكون لإدارة جميع الـ Global Components مثل Toast والـ Modals
const GlobalComponents = () => {
  const {
    // Toast
    toastOpen,
    toastMessage,
    hideToast,
    
    // Modal
    openModal,
    modalType,
    closeModal,
  } = useApp();

  return (
    <>
      {/* Toast */}
      <Toast
        message={toastMessage}
        open={toastOpen}
        onClose={hideToast}
      />

      {/* Modals */}
      {modalType === "login" ? (
        <ModalLogin open={openModal} onClose={closeModal} />
      ) : (
        <ModalRegister open={openModal} onClose={closeModal} />
      )}
    </>
  );
};

export default GlobalComponents;
